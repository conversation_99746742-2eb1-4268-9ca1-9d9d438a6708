import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { BasicService } from '../../service/basic.service';
import { HybridDbService } from '../../shared/hybrid-db.service';
import {
  SectorHierarchyResponse,
  TypologyContent,
  DatacolCategoryTagsResponse,
  AddRemoveTagRequest,
  AddRemoveTagResponse,
  Tag
} from '../model/sector-hierarchy-response';

@Injectable({
  providedIn: 'root'
})
export class CatalogsService extends BasicService {
  private _hybridDbService = inject(HybridDbService);

  constructor() {
    super();
  }

  /**
   * Ottiene la gerarchia dei settori per un catalogo
   * Prima prova a recuperare dal database locale, poi dall'API come fallback
   * @param params Parametri contenenti idCatalog
   * @returns Observable<TypologyContent>
   */
  getSectorHierarchy(params: { idCatalog: number }): Observable<TypologyContent> {
    return new Observable(observer => {
      // Prima prova a caricare dal database locale
      this.isSectorHierarchyAvailable(params.idCatalog)
        .then(isAvailable => {
          if (isAvailable) {
            console.log(`📊 CatalogsService.getSectorHierarchy - Caricamento dal database locale per catalogo ${params.idCatalog}`);
            return this.getSectorHierarchyFromDatabase(params.idCatalog);
          } else {
            console.log(`⚠️ CatalogsService.getSectorHierarchy - Database locale non disponibile, fallback all'API per catalogo ${params.idCatalog}`);
            // Fallback all'API
            const url = `/appcatalogs/getSectorHierarchy?idCatalog=${params.idCatalog}`;
            console.log('🔗 CatalogsService.getSectorHierarchy - URL:', url);

            return this.basicGet(url, false).then((response: any) => {
              const typedResponse = response as SectorHierarchyResponse;
              if (typedResponse.status === 'OK') {
                return typedResponse.content;
              } else {
                throw new Error(typedResponse.error || 'Error loading sector hierarchy from API');
              }
            });
          }
        })
        .then(content => {
          observer.next(content);
          observer.complete();
        })
        .catch(error => {
          console.error('❌ CatalogsService.getSectorHierarchy - Errore:', error);
          observer.error(error);
        });
    });
  }

  /**
   * Ottiene i tag assegnati a una categoria
   * @param params Parametri contenenti idSubCategory e idCatalog
   * @returns Observable<DatacolCategoryTagsResponse>
   */
  getDatacolCategoryTags(params: { idSubCategory: number; idCatalog: number }): Observable<DatacolCategoryTagsResponse> {
    const url = `/appcatalogs/getDatacolCategoryTags?idSubCategory=${params.idSubCategory}&idCatalog=${params.idCatalog}`;
    console.log('🔗 CatalogsService.getDatacolCategoryTags - URL:', url);

    return new Observable(observer => {
      this.basicGet(url, false).then((response: any) => {
        observer.next(response as DatacolCategoryTagsResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Aggiunge un tag a una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  addDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    const url = `/appcatalogs/addDatacolCategoryTag`;
    console.log('🔗 CatalogsService.addDatacolCategoryTag - URL:', url, 'Request:', request);

    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: any) => {
        observer.next(response as AddRemoveTagResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Rimuove un tag da una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  removeDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    const url = `/appcatalogs/removeDatacolCategoryTag`;
    console.log('🔗 CatalogsService.removeDatacolCategoryTag - URL:', url, 'Request:', request);

    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: any) => {
        observer.next(response as AddRemoveTagResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Verifica se la gerarchia è disponibile nel database locale
   * @param idCatalog ID del catalogo
   * @returns Promise<boolean> True se la gerarchia è disponibile
   */
  private async isSectorHierarchyAvailable(idCatalog: number): Promise<boolean> {
    try {
      const records = await this._hybridDbService.getRecordsByANDCondition(
        'sector_hierarchy',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      return records.length > 0;
    } catch (error) {
      console.error(`❌ Errore nella verifica disponibilità gerarchia per catalogo ${idCatalog}:`, error);
      return false;
    }
  }

  /**
   * Recupera la gerarchia dei settori dal database locale
   * @param idCatalog ID del catalogo
   * @returns Promise<TypologyContent> Gerarchia dei settori
   */
  private async getSectorHierarchyFromDatabase(idCatalog: number): Promise<TypologyContent> {
    try {
      console.log(`📊 Recupero gerarchia settori dal database per catalogo ${idCatalog}...`);

      // Recupera tutti i tag per questo catalogo dal database
      const tagRecords = await this._hybridDbService.getRecordsByANDCondition(
        'sector_hierarchy',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      if (tagRecords.length === 0) {
        console.warn(`⚠️ Nessuna gerarchia trovata nel database per catalogo ${idCatalog}`);
        return { tags: [] };
      }

      // Costruisce la gerarchia dai record del database
      const hierarchy = this.buildHierarchyFromRecords(tagRecords);

      console.log(`✅ Gerarchia settori recuperata dal database per catalogo ${idCatalog}`);
      return hierarchy;

    } catch (error) {
      console.error(`❌ Errore nel recupero della gerarchia dal database per catalogo ${idCatalog}:`, error);
      throw error;
    }
  }

  /**
   * Costruisce la gerarchia dei tag dai record del database
   * @param records Record dal database
   * @returns TypologyContent Gerarchia costruita
   */
  private buildHierarchyFromRecords(records: any[]): TypologyContent {
    try {
      // Separa il tag universale dai tag normali
      const universalTag = records.find(r => r.level === '0');
      const normalTags = records.filter(r => r.level !== '0');

      // Raggruppa i tag per livello
      const tagsByLevel = new Map<string, any[]>();
      normalTags.forEach(record => {
        const level = record.level;
        if (!tagsByLevel.has(level)) {
          tagsByLevel.set(level, []);
        }
        tagsByLevel.get(level)!.push(record);
      });

      // Costruisce la gerarchia partendo dal livello 1 (settori)
      const rootTags = this.buildTagLevel(tagsByLevel, '1', null);

      const result: TypologyContent = {
        tags: rootTags
      };

      // Aggiunge il tag universale se presente
      if (universalTag) {
        result.universalTag = this.recordToTag(universalTag);
      }

      return result;

    } catch (error) {
      console.error('❌ Errore nella costruzione della gerarchia dai record:', error);
      return { tags: [] };
    }
  }

  /**
   * Costruisce ricorsivamente un livello della gerarchia
   * @param tagsByLevel Mappa dei tag raggruppati per livello
   * @param currentLevel Livello corrente da costruire
   * @param parentIdTag ID del tag padre (null per il livello radice)
   * @returns Tag[] Array di tag per il livello corrente
   */
  private buildTagLevel(tagsByLevel: Map<string, any[]>, currentLevel: string, parentIdTag: string | null): Tag[] {
    const currentLevelTags = tagsByLevel.get(currentLevel) || [];

    // Filtra i tag che appartengono al padre specificato
    const relevantTags = currentLevelTags.filter(record => {
      if (parentIdTag === null) {
        return record.parentIdTag === null || record.parentIdTag === '';
      }
      return record.parentIdTag === parentIdTag;
    });

    return relevantTags.map(record => {
      const tag = this.recordToTag(record);

      // Costruisce ricorsivamente i sottotag per il livello successivo
      const nextLevel = String(parseInt(currentLevel) + 1);
      if (tagsByLevel.has(nextLevel)) {
        tag.subTags = this.buildTagLevel(tagsByLevel, nextLevel, record.idTag);
      }

      return tag;
    });
  }

  /**
   * Converte un record del database in un oggetto Tag
   * @param record Record dal database
   * @returns Tag Oggetto tag
   */
  private recordToTag(record: any): Tag {
    return {
      idTag: parseInt(record.idTag),
      keyTag: record.keyTag,
      description: record.description,
      subTags: []
    };
  }
}
